{"registry-mirrors": ["https://docker.m.daocloud.io", "https://docker.1ms.run", "https://docker-0.unsee.tech", "https://hub.xdark.top", "https://hub.fast360.xyz", "https://docker.xuanyuan.me", "https://docker.tbedu.top", "https://docker.hlmirror.com", "https://doublezonline.cloud", "https://docker.melikeme.cn", "https://image.cloudlayer.icu", "https://dislabaiot.xyz", "https://freeno.xyz", "https://docker.kejilion.pro", "https://ccr.ccs.tencentyun.com", "https://docker.mybacc.com", "https://dytt.online", "https://lispy.org", "https://docker.xiaogenban1993.com", "https://docker.yomansunter.com", "https://aicarbon.xyz", "https://666860.xyz", "https://a.ussh.net", "https://hub.littlediary.cn", "https://hub.rat.dev"], "max-concurrent-downloads": 10, "max-concurrent-uploads": 5, "max-download-attempts": 5, "storage-driver": "overlay2", "storage-opts": ["overlay2.override_kernel_check=true"], "log-driver": "json-file", "log-opts": {"max-size": "100m", "max-file": "3"}, "live-restore": true, "userland-proxy": false, "experimental": false, "metrics-addr": "127.0.0.1:9323", "default-ulimits": {"nofile": {"Name": "nofile", "Hard": 64000, "Soft": 64000}}, "default-shm-size": "128M", "containerd-namespace": "docker", "containerd-plugins-namespace": "docker-plugins", "data-root": "/var/lib/docker", "exec-opts": ["native.cgroupdriver=systemd"], "bridge": "docker0", "fixed-cidr": "**********/16", "default-gateway": "", "dns": ["*******", "***************", "*********"], "mtu": 1500, "icc": true, "ip-forward": true, "ip-masq": true, "iptables": true, "ipv6": false, "features": {"buildkit": true}, "builder": {"gc": {"enabled": true, "defaultKeepStorage": "20GB"}}}